"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export interface Organization {
  id: string;
  name: string;
  domain?: string | null;
  logo?: string | null;
  role?: string;
  status?: string;
}

interface OrganizationContextType {
  currentOrganization: Organization | null;
  organizations: Organization[];
  setCurrentOrganization: (org: Organization) => void;
  isLoading: boolean;
  refreshOrganizations: () => Promise<void>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined,
);

export function OrganizationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [currentOrganization, setCurrentOrganization] =
    useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchOrganizations = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/organizations");
      if (response.ok) {
        const data = await response.json();
        setOrganizations(data.organizations);

        // Prioritize current organization from server response over localStorage
        const defaultOrg = data.currentOrganization || data.organizations[0];

        if (data.currentOrganization) {
          // If server provides current organization, use it and update localStorage
          setCurrentOrganization(data.currentOrganization);
          localStorage.setItem("currentOrganizationId", data.currentOrganization.id);
        } else {
          // Fallback to localStorage only if server doesn't provide current org
          const storedOrgId = localStorage.getItem("currentOrganizationId");
          if (storedOrgId) {
            const storedOrg = data.organizations.find(
              (org: Organization) => org.id === storedOrgId,
            );
            if (storedOrg) {
              setCurrentOrganization(storedOrg);
            } else {
              setCurrentOrganization(defaultOrg);
              localStorage.setItem("currentOrganizationId", defaultOrg.id);
            }
          } else {
            setCurrentOrganization(defaultOrg);
            localStorage.setItem("currentOrganizationId", defaultOrg.id);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching organizations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganizations();
  }, []);

  // Monitor for user changes (login/logout) by watching the user-info cookie
  // Monitor for user changes (login/logout) by watching the session
  useEffect(() => {
    if (status === "loading") {
      return; // Wait for session to load
    }

    if (session?.user) {
      // User is authenticated, fetch organizations
      fetchOrganizations();
    } else {
      // User is not authenticated, clear organization context
      console.log("User logged out, clearing organization context");
      setCurrentOrganization(null);
      setOrganizations([]);
      setIsLoading(false);
    }
  }, [session, status]);

  const handleSetCurrentOrganization = (org: Organization) => {
    setCurrentOrganization(org);
    // Note: Organization selection is now managed through secure session
    // No need to store in localStorage for security reasons
  };

  return (
    <OrganizationContext.Provider
      value={{
        currentOrganization,
        organizations,
        setCurrentOrganization: handleSetCurrentOrganization,
        isLoading,
        refreshOrganizations: fetchOrganizations,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider",
    );
  }
  return context;
}
