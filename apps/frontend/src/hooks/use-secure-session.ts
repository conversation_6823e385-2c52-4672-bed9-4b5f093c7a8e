"use client";

/**
 * Client-side secure session hooks using NextAuth
 * Replaces custom cookie-based authentication on the client side
 */

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

export interface ClientUserSession {
  id: string;
  email: string;
  name: string;
  role: string;
  organizationId: string;
}

/**
 * Hook to get current user session on client side
 * Replaces useAuth and custom cookie reading
 */
export function useSecureSession() {
  const { data: session, status } = useSession();
  
  const user: ClientUserSession | null = session?.user ? {
    id: session.user.id,
    email: session.user.email || "",
    name: session.user.name || "",
    role: session.user.role,
    organizationId: session.user.organizationId,
  } : null;

  return {
    user,
    isAuthenticated: !!session?.user,
    isLoading: status === "loading",
    status,
  };
}

/**
 * Hook to get user role from secure session
 * Replaces useUserRole hook that reads from cookies
 */
export function useSecureUserRole() {
  const { user, isLoading } = useSecureSession();
  
  return {
    role: user?.role || null,
    isLoading,
    hasRole: (role: string) => user?.role === role,
  };
}

/**
 * Hook to get organization context from secure session
 * Replaces organization context that reads from cookies
 */
export function useSecureOrganizationContext() {
  const { user, isLoading } = useSecureSession();
  const [organizationDetails, setOrganizationDetails] = useState<{
    name?: string;
    slug?: string;
  } | null>(null);
  const [currentBranch, setCurrentBranch] = useState<{
    id?: string;
    name?: string;
  } | null>(null);

  // Fetch additional organization details when user is available
  useEffect(() => {
    if (user?.organizationId && !isLoading) {
      // Fetch organization details from API
      fetch(`/api/organization/${user.organizationId}`)
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            setOrganizationDetails({
              name: data.organization.name,
              slug: data.organization.slug,
            });
          }
        })
        .catch(error => {
          console.error("Error fetching organization details:", error);
        });

      // Fetch current branch details
      fetch(`/api/user/current-branch`)
        .then(res => res.json())
        .then(data => {
          if (data.success && data.branch) {
            setCurrentBranch({
              id: data.branch.id,
              name: data.branch.name,
            });
          }
        })
        .catch(error => {
          console.error("Error fetching current branch:", error);
        });
    }
  }, [user?.organizationId, isLoading]);

  return {
    organizationId: user?.organizationId || null,
    organizationName: organizationDetails?.name || null,
    organizationSlug: organizationDetails?.slug || null,
    currentRole: user?.role || null,
    currentBranchId: currentBranch?.id || null,
    currentBranchName: currentBranch?.name || null,
    isLoading,
  };
}

/**
 * Hook to get current branch from secure session
 * Replaces useCurrentBranch hook that reads from cookies
 */
export function useSecureCurrentBranch() {
  const { user, isLoading } = useSecureSession();
  const [branch, setBranch] = useState<{
    id: string;
    name: string;
    isHeadOffice?: boolean;
  } | null>(null);
  const [branchLoading, setBranchLoading] = useState(false);

  useEffect(() => {
    if (user && !isLoading) {
      setBranchLoading(true);
      
      fetch(`/api/user/current-branch`)
        .then(res => res.json())
        .then(data => {
          if (data.success && data.branch) {
            setBranch({
              id: data.branch.id,
              name: data.branch.name,
              isHeadOffice: data.branch.isHeadOffice,
            });
          }
        })
        .catch(error => {
          console.error("Error fetching current branch:", error);
        })
        .finally(() => {
          setBranchLoading(false);
        });
    }
  }, [user, isLoading]);

  const updateBranch = async (branchId: string) => {
    try {
      const response = await fetch(`/api/user/update-branch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Refresh branch data
        setBranchLoading(true);
        const branchResponse = await fetch(`/api/user/current-branch`);
        const branchData = await branchResponse.json();
        
        if (branchData.success && branchData.branch) {
          setBranch({
            id: branchData.branch.id,
            name: branchData.branch.name,
            isHeadOffice: branchData.branch.isHeadOffice,
          });
        }
        setBranchLoading(false);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error("Error updating branch:", error);
      return false;
    }
  };

  return {
    branch,
    isLoading: isLoading || branchLoading,
    updateBranch,
  };
}

/**
 * Hook for authentication actions
 * Provides secure login/logout functionality
 */
export function useSecureAuth() {
  const { status } = useSession();
  
  return {
    isLoading: status === "loading",
    isAuthenticated: status === "authenticated",
  };
}
