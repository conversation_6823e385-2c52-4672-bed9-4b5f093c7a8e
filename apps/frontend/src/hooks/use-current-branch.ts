import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export function useCurrentBranch() {
  const [currentBranch, setCurrentBranch] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to update the current branch via API
  const updateCurrentBranch = async (branchId: string) => {
    try {
      await fetch("/api/branches/default", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId }),
      });
    } catch (error) {
      console.error("Error updating current branch:", error);
    }
  };

  const { data: session, status } = useSession();

  useEffect(() => {
    const getBranchFromSession = () => {
      setIsLoading(true);

      // Wait for session to load
      if (status === "loading") {
        return;
      }

      if (!session?.user) {
        setIsLoading(false);
        return;
      }

      // Fetch current branch from API using secure session
      fetchBranchFromAPI();
    };

    const fetchBranchFromAPI = async () => {
      try {
        // First try to get the current branch
        const currentBranchResponse = await fetch("/api/branches");
        const data = await currentBranchResponse.json();

        if (currentBranchResponse.ok) {
          if (data.currentBranch) {
            // If we have a current branch, use it
            setCurrentBranch(data.currentBranch);
          } else if (data.branches?.length > 0) {
            // Otherwise, use the first branch or head office
            const headOffice = data.branches.find((b: any) => b.isHeadOffice);
            const branch = headOffice || data.branches[0];

            setCurrentBranch(branch);

            // Set this as the current branch
            await updateCurrentBranch(branch.id);
          } else {
            setError("No branches available");
          }
        } else {
          setError("Failed to fetch branches");
        }
      } catch (error) {
        console.error("Error fetching branch data:", error);
        setError("Failed to fetch branches");
      } finally {
        setIsLoading(false);
      }
    };

    getBranchFromSession();
  }, [session, status]);

  // Wrapper for setCurrentBranch that also updates the API
  const setCurrent = async (branch: any) => {
    setCurrentBranch(branch);
    await updateCurrentBranch(branch.id);
  };

  return { currentBranch, isLoading, error, setCurrentBranch: setCurrent };
}
