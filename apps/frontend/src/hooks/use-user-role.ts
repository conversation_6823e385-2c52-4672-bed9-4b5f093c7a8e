"use client";

import { useSession } from "next-auth/react";
import { UserRole } from "@/config/roleBasedSidebarConfig";

export function useUserRole() {
  const { data: session, status } = useSession();

  // Get role from secure NextAuth session only
  const userRole = session?.user?.role;

  // If the session is loading, return loading state
  if (status === "loading") {
    return {
      role: undefined,
      isHospitalAdmin: false,
      isBranchAdmin: false,
      isDoctor: false,
      isNurse: false,
      isReceptionist: false,
      isUser: false,
      isAuthenticated: false,
      isLoading: true,
    };
  }

  return {
    role: userRole as UserRole | undefined,
    isSuperAdmin: userRole === "superAdmin",
    isHospitalAdmin:
      userRole === "admin" ||
      userRole === "hospitalAdmin" ||
      userRole === "owner",
    isBranchAdmin: userRole === "branchAdmin",
    isDoctor: userRole === "doctor",
    isStaff: userRole === "staff",
    isNurse: userRole === "nurse",
    isReceptionist: userRole === "receptionist",
    isUser: userRole === "user",
    isAuthenticated: !!session?.user,
    isLoading: false,
  };
}
