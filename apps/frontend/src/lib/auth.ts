import { compare, hash as bcryptHash } from "bcryptjs";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "./prisma";

export async function hashPassword(password: string) {
  return await bcryptHash(password, 10);
}

export async function comparePassword(
  password: string,
  hashedPassword: string,
) {
  try {
    const result = await compare(password, hashedPassword);
    return result;
  } catch (error) {
    console.error("Error comparing passwords:", error);
    return false;
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: {
            organizations: {
              select: {
                organizationId: true,
                isDefault: true,
                roles: true,
              },
            },
          },
        });

        if (!user || !user.password) {
          return null;
        }

        const isPasswordValid = await comparePassword(
          credentials.password,
          user.password,
        );

        if (!isPasswordValid) {
          return null;
        }

        // Find default organization or use the first one
        const defaultOrg =
          user.organizations.find((org) => org.isDefault) ||
          user.organizations[0];

        // Get the role from the default organization
        const userRoles = Array.isArray(defaultOrg?.roles)
          ? (defaultOrg.roles as string[])
          : [];
        const primaryRole = userRoles[0] || "user"; // Use first role as primary role

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: primaryRole,
          organizationId: defaultOrg?.organizationId || "",
        };
      },
    }),
  ],
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async session({ session, user }) {
      console.log("Session callback:", { session, user });
      if (session.user && user) {
        // Get user's organization and role information
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id },
          include: {
            organizations: {
              select: {
                organizationId: true,
                isDefault: true,
                roles: true,
              },
            },
          },
        });

        if (dbUser) {
          const defaultOrg =
            dbUser.organizations.find((org) => org.isDefault) ||
            dbUser.organizations[0];

          const userRoles = Array.isArray(defaultOrg?.roles)
            ? (defaultOrg.roles as string[])
            : [];
          const primaryRole = userRoles[0] || "user";

          session.user.id = user.id;
          session.user.role = primaryRole;
          session.user.organizationId = defaultOrg?.organizationId || "";
        }
      }
      return session;
    },
  },
  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/sign-in",
  },
};
