/**
 * Secure session management utilities using NextAuth
 * Replaces custom cookie-based authentication with secure NextAuth sessions
 */

import { getServerSession } from "next-auth";
import { authOptions } from "./auth";
import { prisma } from "./prisma";

export interface SecureUserSession {
  id: string;
  email: string;
  name: string;
  role: string;
  organizationId: string;
  organizationName?: string;
  currentBranchId?: string;
}

/**
 * Get the current user session on the server side
 * Replaces getUserFromCookies and getUserInfoFromCookies
 */
export async function getSecureUserSession(): Promise<SecureUserSession | null> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return null;
    }

    // Get additional user information from database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        organizations: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          where: {
            isDefault: true,
          },
          take: 1,
        },
      },
    });

    if (!user) {
      return null;
    }

    const defaultOrg = user.organizations[0];
    
    return {
      id: session.user.id,
      email: session.user.email || user.email || "",
      name: session.user.name || user.name || "",
      role: session.user.role,
      organizationId: session.user.organizationId,
      organizationName: defaultOrg?.organization?.name,
      currentBranchId: user.currentBranchId || undefined,
    };
  } catch (error) {
    console.error("Error getting secure user session:", error);
    return null;
  }
}

/**
 * Check if user has a valid session
 * Replaces hasValidSession from organization-context-cookies
 */
export async function hasValidSecureSession(): Promise<boolean> {
  try {
    const session = await getSecureUserSession();
    return !!(session && session.email);
  } catch (error) {
    console.error("Error checking session validity:", error);
    return false;
  }
}

/**
 * Get organization context from secure session
 * Replaces getCurrentOrganizationContext
 */
export async function getOrganizationContext() {
  try {
    const userSession = await getSecureUserSession();
    
    if (!userSession) {
      return null;
    }

    // Get additional organization details
    const organization = await prisma.organization.findUnique({
      where: { id: userSession.organizationId },
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });

    // Get current branch info if available
    let currentBranch = null;
    if (userSession.currentBranchId) {
      currentBranch = await prisma.branch.findUnique({
        where: { id: userSession.currentBranchId },
        select: {
          id: true,
          name: true,
          isHeadOffice: true,
        },
      });
    }

    return {
      organizationId: userSession.organizationId,
      organizationName: organization?.name || userSession.organizationName || "",
      organizationSlug: organization?.slug,
      currentRole: userSession.role,
      currentBranchId: currentBranch?.id,
      currentBranchName: currentBranch?.name,
      currentBranchIsHeadOffice: currentBranch?.isHeadOffice,
    };
  } catch (error) {
    console.error("Error getting organization context:", error);
    return null;
  }
}

/**
 * Get current branch from secure session
 * Replaces getCurrentBranchFromCookies
 */
export async function getCurrentBranchFromSession(): Promise<string | null> {
  try {
    const userSession = await getSecureUserSession();
    return userSession?.currentBranchId || null;
  } catch (error) {
    console.error("Error getting current branch from session:", error);
    return null;
  }
}

/**
 * Get current branch object from secure session
 * Replaces getCurrentBranchObjectFromCookies
 */
export async function getCurrentBranchObjectFromSession() {
  try {
    const userSession = await getSecureUserSession();
    
    if (!userSession?.currentBranchId) {
      return null;
    }

    const branch = await prisma.branch.findUnique({
      where: { id: userSession.currentBranchId },
      select: {
        id: true,
        name: true,
        isHeadOffice: true,
      },
    });

    return branch ? {
      id: branch.id,
      name: branch.name,
      isHeadOffice: branch.isHeadOffice,
    } : null;
  } catch (error) {
    console.error("Error getting current branch object from session:", error);
    return null;
  }
}

/**
 * Update user's current branch in the database
 * This will be reflected in subsequent session calls
 */
export async function updateCurrentBranch(branchId: string): Promise<boolean> {
  try {
    const userSession = await getSecureUserSession();
    
    if (!userSession) {
      return false;
    }

    await prisma.user.update({
      where: { id: userSession.id },
      data: { currentBranchId: branchId },
    });

    return true;
  } catch (error) {
    console.error("Error updating current branch:", error);
    return false;
  }
}

/**
 * Check if user has specific role
 */
export async function hasRole(role: string): Promise<boolean> {
  try {
    const userSession = await getSecureUserSession();
    return userSession?.role === role;
  } catch (error) {
    console.error("Error checking user role:", error);
    return false;
  }
}

/**
 * Check if user belongs to specific organization
 */
export async function belongsToOrganization(organizationId: string): Promise<boolean> {
  try {
    const userSession = await getSecureUserSession();
    return userSession?.organizationId === organizationId;
  } catch (error) {
    console.error("Error checking organization membership:", error);
    return false;
  }
}
