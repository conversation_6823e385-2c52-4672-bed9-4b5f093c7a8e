export const maxDuration = 299;

import { NextResponse } from "next/server";

export async function POST() {
  try {
    // Note: Logout is now handled by NextAuth signOut on the client side
    // This endpoint is kept for backward compatibility but NextAuth handles session cleanup

    return NextResponse.json({
      success: true,
      message: "Please use NextAuth signOut for secure logout",
    });
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
