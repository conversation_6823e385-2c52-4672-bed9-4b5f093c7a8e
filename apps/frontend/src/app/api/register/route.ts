export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { hashPassword } from "@/lib/auth";
import { v4 as uuidv4 } from "uuid";
import { sendVerificationEmail } from "@/lib/email/send-verification-email";

// Helper function to generate a verification token
function generateVerificationToken() {
  return uuidv4();
}

// Helper function to generate a slug from name and current time
function generateSlug(name: string): string {
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const nameSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-") // Replace non-alphanumeric chars with hyphens
    .replace(/^-+|-+$/g, "") // Remove leading/trailing hyphens
    .substring(0, 30); // Limit length

  return `${nameSlug}-${timestamp}`;
}

// Helper function to check if a slug is available
async function isSlugAvailable(slug: string) {
  const existingOrg = await db.organization.findFirst({
    where: { slug },
  });
  return !existingOrg;
}

// Import validation functions
import { validateEmail, validatePassword } from "@/lib/validation";

export async function POST(req: NextRequest) {
  try {
    const {
      name,
      email,
      password,
      companyName,
      createDefaultBranch = false,
      confirmPassword,
    } = await req.json();

    // Validate input
    if (!name || !email || !password || !companyName) {
      return NextResponse.json(
        {
          error: "Name, email, password, company name are required",
        },
        { status: 400 },
      );
    }

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      return NextResponse.json(
        {
          error:
            emailValidation.message || "Please enter a valid email address",
        },
        { status: 400 },
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        {
          error:
            passwordValidation.message || "Password does not meet requirements",
        },
        { status: 400 },
      );
    }

    // Validate password confirmation
    if (confirmPassword && password !== confirmPassword) {
      return NextResponse.json(
        { error: "Passwords do not match" },
        { status: 400 },
      );
    }

    // Generate a slug from the company name
    let slug = generateSlug(companyName);

    // Ensure the slug is unique
    let isSlugUnique = await isSlugAvailable(slug);
    let attempts = 0;

    // If slug is not unique, try adding a random suffix until it is
    while (!isSlugUnique && attempts < 5) {
      slug = generateSlug(companyName); // This will generate a new timestamp
      isSlugUnique = await isSlugAvailable(slug);
      attempts++;
    }

    // If we still don't have a unique slug after 5 attempts, add a UUID
    if (!isSlugUnique) {
      slug = `${slug}-${uuidv4().substring(0, 8)}`;
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Generate verification token
    const verificationToken = generateVerificationToken();

    // Create organization with onboardingCompleted set based on createDefaultBranch
    const organization = await db.organization.create({
      data: {
        name: companyName,
        slug: slug,
        status: "inactive", // Set organization to inactive status by default
        onboardingCompleted: createDefaultBranch, // Set to true if creating default branch
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // Create default branch if requested
    if (createDefaultBranch) {
      await db.branch.create({
        data: {
          name: companyName,
          facilityType: "hospital",
          isHeadOffice: true,
          organizationId: organization.id,
        },
      });
    }

    // Store verification token
    await db.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });

    // Send verification email - log errors but continue with registration
    let emailSent = false;
    let emailError = null;
    try {
      const emailResult = await sendVerificationEmail({
        email,
        hospitalName: companyName,
        adminName: name,
        token: verificationToken,
      });

      if (emailResult.success) {
        emailSent = true;
        console.log("✅ Verification email sent successfully to:", email);
      } else {
        emailError = emailResult.error;
        console.error(
          "❌ Failed to send verification email:",
          emailResult.error,
        );
      }
    } catch (error) {
      emailError =
        error instanceof Error ? error.message : "Unknown email error";
      console.error("❌ Exception while sending verification email:", error);
    }

    // Proceed with user creation after email verification setup

    // Create user
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: "admin", // Set as admin for healthcare facility
        organizations: {
          create: {
            organizationId: organization.id,
            roles: ["hospitalAdmin"],
            isDefault: true,
          },
        },
      },
    });

    // Note: Session management is now handled by NextAuth
    // No need to create custom session tokens or cookies

    // Return user without password and include verification info
    const { password: _, ...userWithoutPassword } = user;

    // Prepare response message based on email status
    let message = "Account created successfully.";
    if (emailSent) {
      message += " Please check your email to verify your account.";
    } else {
      message +=
        " However, we couldn't send the verification email. Please contact support.";
    }

    return NextResponse.json(
      {
        ...userWithoutPassword,
        organization,
        verificationRequired: true,
        emailSent,
        emailError: emailError || undefined,
        message,
      },
      { status: 201 },
    );
  } catch (error) {
    // Return a generic error message to avoid exposing sensitive information
    return NextResponse.json(
      { error: "Something went wrong during registration. Please try again." },
      { status: 500 },
    );
  }
}
