export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { comparePassword } from "@/lib/auth";
import { cookies } from "next/headers";
import { v4 as uuidv4 } from "uuid";

export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 },
      );
    }

    // Try to find the user in the database
    const dbUser = await db.user.findUnique({
      where: { email },
      include: {
        organizations: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
                status: true,
                logo: true,
              },
            },
          },
          where: {
            isDefault: true,
          },
          take: 1,
        },
      },
    });

    let user = null;
    let isValidPassword = false;
    let organization = null;

    // For demo purposes, hardcode the credentials check
    if (email === "<EMAIL>" && password === "demo123") {
      // Get the default organization for the demo user
      const demoUser = await db.user.findUnique({
        where: { email: "<EMAIL>" },
        include: {
          organizations: {
            include: {
              organization: true,
            },
            where: {
              isDefault: true,
            },
            take: 1,
          },
        },
      });

      organization = demoUser?.organizations[0]?.organization || {
        id: "default-org-id",
        name: "Default Organization",
      };

      // Get the role from the default organization for demo user
      const demoUserOrg = demoUser?.organizations[0];
      const demoUserRoles = Array.isArray(demoUserOrg?.roles)
        ? (demoUserOrg.roles as string[])
        : ["user"];
      const demoPrimaryRole = demoUserRoles[0] || "user";

      user = {
        id: "demo-user-id",
        email: "<EMAIL>",
        name: "Demo User",
        role: demoPrimaryRole,
      };
      isValidPassword = true;
    } else if (email === "<EMAIL>" && password === "admin123") {
      // Get the default organization for the admin user
      const adminUser = await db.user.findUnique({
        where: { email: "<EMAIL>" },
        include: {
          organizations: {
            include: {
              organization: true,
            },
            where: {
              isDefault: true,
            },
            take: 1,
          },
        },
      });

      organization = adminUser?.organizations[0]?.organization || {
        id: "default-org-id",
        name: "Default Organization",
      };

      user = {
        id: "admin-user-id",
        email: "<EMAIL>",
        name: "Admin User",
        role: "admin",
      };
      isValidPassword = true;
    } else if (dbUser && dbUser.password) {
      // Check if email is verified
      if (!dbUser.emailVerified) {
        return NextResponse.json(
          {
            error: "Please verify your email before signing in",
            verificationRequired: true,
            email: dbUser.email,
          },
          { status: 403 },
        );
      }

      isValidPassword = await comparePassword(password, dbUser.password);
      if (isValidPassword) {
        // Get the role from the default organization
        const defaultUserOrg = dbUser.organizations[0];
        const userRoles = Array.isArray(defaultUserOrg?.roles)
          ? (defaultUserOrg.roles as string[])
          : [];
        const primaryRole = userRoles[0] || "user"; // Use first role as primary role

        user = {
          id: dbUser.id,
          email: dbUser.email,
          name: dbUser.name,
          role: primaryRole,
        };
        organization = dbUser.organizations[0]?.organization || null;

        // Check if the default organization is active
        if (organization && organization.status === "inactive") {
          // Find an active organization for the user
          const activeUserOrg = await db.userOrganization.findFirst({
            where: {
              userId: dbUser.id,
              organization: {
                status: "active",
              },
            },
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  status: true,
                  logo: true,
                },
              },
            },
          });

          if (activeUserOrg) {
            // Use the active organization instead
            organization = activeUserOrg.organization;
            const activeUserRoles = Array.isArray(activeUserOrg.roles)
              ? (activeUserOrg.roles as string[])
              : [];
            const activePrimaryRole = activeUserRoles[0] || "user";
            user.role = activePrimaryRole;
          } else {
            // No active organizations found - will be handled by middleware
            // Continue with the inactive organization for now
          }
        }

        // For doctors, ensure they have a current branch set
        if (primaryRole === "doctor" && !dbUser.currentBranchId) {
          // Get the doctor's first assigned branch in the default organization
          const doctor = await db.doctor.findFirst({
            where: {
              userId: dbUser.id,
              organizationId: organization?.id,
            },
            include: {
              branches: {
                include: {
                  branch: true,
                },
                take: 1,
              },
            },
          });

          if (doctor?.branches && doctor.branches.length > 0) {
            // Set the first branch as current branch
            await db.user.update({
              where: { id: dbUser.id },
              data: {
                currentBranchId: doctor.branches[0].branchId,
              },
            });
          }
        }
      }
    }

    if (!user || !isValidPassword) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 },
      );
    }

    // Create a session
    const sessionToken = uuidv4();
    const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

    // Set a cookie
    cookies().set("session-token", sessionToken, {
      expires,
      httpOnly: true,
      path: "/",
    });

    // Get updated user data with current branch and enhanced organization info
    let currentBranchId = null;
    let currentBranchInfo = null;

    if (user.role === "doctor") {
      const updatedUser = await db.user.findUnique({
        where: { id: user.id },
        select: { currentBranchId: true },
      });
      currentBranchId = updatedUser?.currentBranchId;
    }

    // Get current branch information for cookie
    if (currentBranchId) {
      const branchData = await db.branch.findUnique({
        where: { id: currentBranchId },
        select: { id: true, name: true, isHeadOffice: true },
      });
      if (branchData) {
        currentBranchInfo = {
          id: branchData.id,
          name: branchData.name,
          isHeadOffice: branchData.isHeadOffice || false,
        };
      }
    } else if (organization?.id) {
      // For non-doctors or doctors without assigned branches, get the head office
      const headOfficeBranch = await db.branch.findFirst({
        where: {
          organizationId: organization.id,
          isHeadOffice: true,
        },
        select: { id: true, name: true, isHeadOffice: true },
      });
      if (headOfficeBranch) {
        currentBranchInfo = {
          id: headOfficeBranch.id,
          name: headOfficeBranch.name,
          isHeadOffice: true,
        };
        currentBranchId = headOfficeBranch.id;
      }
    }

    // Set comprehensive cookies for enhanced session management

    // 1. User info cookie (existing, enhanced)
    cookies().set(
      "user-info",
      JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        organizationId: organization?.id,
        organizationName: organization?.name,
        currentBranchId,
      }),
      {
        expires,
        path: "/",
      },
    );

    // 2. Current branch cookie
    if (currentBranchInfo) {
      cookies().set("current-branch", JSON.stringify(currentBranchInfo), {
        expires,
        path: "/",
      });
    }

    // 3. Organization context cookie
    if (organization) {
      cookies().set(
        "org-context",
        JSON.stringify({
          id: organization.id,
          name: organization.name,
          slug:
            organization.slug ||
            organization.name.toLowerCase().replace(/\s+/g, "-"),
        }),
        {
          expires,
          path: "/",
        },
      );
    }

    // 4. Current role cookie
    cookies().set("current-role", user.role, {
      expires,
      path: "/",
    });

    // Determine redirect URL based on user role
    let redirectUrl = "/dashboard";
    if (user.role === "superAdmin") {
      redirectUrl = "/admin";
    }

    return NextResponse.json({
      success: true,
      user,
      redirectUrl,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Something went wrong during login. Please try again." },
      { status: 500 },
    );
  }
}
