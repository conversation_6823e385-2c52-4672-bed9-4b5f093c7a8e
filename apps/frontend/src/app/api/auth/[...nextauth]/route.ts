export const maxDuration = 299;

import NextAuth, { DefaultSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// Extend the types
declare module "next-auth" {
  interface User {
    id: string;
    role: string;
    organizationId: string;
  }

  interface Session {
    user: {
      id: string;
      role: string;
      organizationId: string;
    } & DefaultSession["user"];
  }
}

// Create the handler using imported config
const handler = NextAuth(authOptions);

// Export the handler functions
export const GET = handler;
export const POST = handler;
