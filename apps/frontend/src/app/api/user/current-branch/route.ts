export const maxDuration = 299;

import { NextResponse } from "next/server";
import { getSecureUserSession } from "@/lib/secure-session";
import { db } from "@/lib/db";

// GET /api/user/current-branch
export async function GET() {
  try {
    const userSession = await getSecureUserSession();

    if (!userSession) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get current branch from user's currentBranchId
    if (!userSession.currentBranchId) {
      return NextResponse.json({
        success: true,
        branch: null,
        message: "No current branch set"
      });
    }

    const branch = await db.branch.findUnique({
      where: { id: userSession.currentBranchId },
      select: {
        id: true,
        name: true,
        isHeadOffice: true,
      },
    });

    if (!branch) {
      return NextResponse.json({
        success: true,
        branch: null,
        message: "Branch not found"
      });
    }

    return NextResponse.json({
      success: true,
      branch: {
        id: branch.id,
        name: branch.name,
        isHeadOffice: branch.isHeadOffice,
      },
    });
  } catch (error) {
    console.error("Error getting current branch:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/user/current-branch - Deprecated, use /api/user/update-branch instead
export async function POST() {
  return NextResponse.json(
    {
      error: "This endpoint is deprecated. Use /api/user/update-branch instead.",
      redirectTo: "/api/user/update-branch"
    },
    { status: 410 }
  );
}
