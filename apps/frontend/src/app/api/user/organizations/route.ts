export const maxDuration = 299;

import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { getUserOrganizations } from "@/lib/organization-roles";
import { db } from "@/lib/db";

/**
 * GET /api/user/organizations
 * Fetch all organizations where the user has any role
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database
    const user = await db.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get user's organizations with roles
    const userOrganizations = await getUserOrganizations(user.id);

    // Format the response
    const organizations = userOrganizations.map((userOrg) => ({
      id: userOrg.organization.id,
      organizationId: userOrg.organization.id,
      name: userOrg.organization.name,
      slug: userOrg.organization.slug,
      logo: userOrg.organization.logo,
      status: userOrg.organization.status,
      roles: Array.isArray(userOrg.roles) ? userOrg.roles : [userOrg.roles],
      isDefault: userOrg.isDefault,
      joinedAt: userOrg.createdAt,
      organization: userOrg.organization, // Include full organization object for compatibility
    }));

    // Get current organization (default one)
    const currentOrganization =
      organizations.find((org) => org.isDefault) || organizations[0];

    return NextResponse.json({
      organizations,
      currentOrganization,
      totalCount: organizations.length,
    });
  } catch (error) {
    console.error("Error fetching user organizations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
