export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getSecureUserSession, updateCurrentBranch } from "@/lib/secure-session";
import { db } from "@/lib/db";

export async function POST(req: NextRequest) {
  try {
    const userSession = await getSecureUserSession();
    
    if (!userSession) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const { branchId } = await req.json();

    if (!branchId) {
      return NextResponse.json(
        { error: "Branch ID is required" },
        { status: 400 }
      );
    }

    // Get user with role information for access validation
    const userWithRole = await db.user.findUnique({
      where: { id: userSession.id },
      include: {
        doctors: {
          include: {
            branches: {
              where: { branchId },
              include: {
                branch: true,
              },
            },
          },
        },
        organizations: {
          include: {
            organization: {
              include: {
                branches: {
                  where: { id: branchId },
                },
              },
            },
          },
        },
      },
    });

    if (!userWithRole) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    let branch = null;

    // Role-based branch access validation
    if (userWithRole.role === "doctor") {
      // For doctors, check if they are assigned to this branch
      if (
        !userWithRole.doctors ||
        !userWithRole.doctors.branches ||
        userWithRole.doctors.branches.length === 0
      ) {
        return NextResponse.json(
          { error: "Access denied. Doctor is not assigned to this branch." },
          { status: 403 }
        );
      }
      const doctorBranch = userWithRole.doctors.branches.find(
        (doctorBranch) => doctorBranch.branchId === branchId
      );
      if (!doctorBranch) {
        return NextResponse.json(
          { error: "Access denied. Doctor is not assigned to this branch." },
          { status: 403 }
        );
      }
      branch = doctorBranch.branch;
      console.log(
        `Doctor ${userSession.email} switching to assigned branch: ${branch.name}`
      );
    } else {
      // For admins and staff, check if branch exists in their organization
      const orgBranches = userWithRole.organizations.flatMap(
        (userOrg) => userOrg.organization.branches
      );
      branch = orgBranches.find((b) => b.id === branchId);

      if (!branch) {
        return NextResponse.json(
          { error: "Branch not found or access denied" },
          { status: 404 }
        );
      }
    }

    // Update the user's current branch in the database
    const success = await updateCurrentBranch(branchId);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to update current branch" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      branch: {
        id: branch.id,
        name: branch.name,
        isHeadOffice: branch.isHeadOffice,
      },
      message: "Current branch updated successfully"
    });
  } catch (error) {
    console.error("Error updating current branch:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
