"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

export default function SignIn() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [verificationRequired, setVerificationRequired] =
    useState<boolean>(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for error in URL params
    const errorParam = searchParams?.get("error");
    if (errorParam === "CredentialsSignin") {
      setError("Invalid email or password");
    }

    // Check if user just verified their email
    const verified = searchParams?.get("verified");
    if (verified === "true") {
      setSuccessMessage(
        "Your email has been verified successfully! You can now sign in.",
      );
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Attempt to sign in with the provided credentials

      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || "Invalid email or password");
        setVerificationRequired(data.verificationRequired || false);
        setIsLoading(false);
        return;
      }

      // Store user preference for remember me
      if (rememberMe) {
        localStorage.setItem("rememberEmail", email);
      } else {
        localStorage.removeItem("rememberEmail");
      }

      // Redirect based on user role
      const redirectUrl = data.redirectUrl || "/";
      router.push(redirectUrl);
      router.refresh();
    } catch (error) {
      setError("An unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            Aran Care
          </h1>
          <h2 className="mt-6 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?{" "}
            <Link href="/signup">
              <Button variant="link" className="p-0 h-auto">
                Register your hospital
              </Button>
            </Link>
          </p>
        </div>

        {error && (
          <div
            className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-lg shadow-sm relative animate-fade-in"
            role="alert"
          >
            <span className="block text-sm font-medium">{error}</span>
            {verificationRequired && (
              <div className="mt-2">
                <Button
                  variant="link"
                  className="p-0 h-auto text-xs text-destructive hover:text-destructive/80 transition-colors"
                  onClick={async () => {
                    try {
                      const response = await fetch("/api/resend-verification", {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ email }),
                      });

                      const data = await response.json();

                      if (response.ok) {
                        setError(null);
                        setVerificationRequired(false);
                        setSuccessMessage(
                          "Verification email has been resent. Please check your inbox.",
                        );
                      } else {
                        setError(
                          data.error || "Failed to resend verification email",
                        );
                      }
                    } catch (error) {
                      console.error(
                        "Error resending verification email:",
                        error,
                      );
                      setError("An unexpected error occurred");
                    }
                  }}
                >
                  Resend verification email
                </Button>
              </div>
            )}
          </div>
        )}

        {successMessage && (
          <div
            className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-sm relative animate-fade-in"
            role="alert"
          >
            <span className="block text-sm font-medium">{successMessage}</span>
          </div>
        )}

        <Card className="border shadow-lg glass-card animate-fade-in">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold gradient-text">
              Sign In
            </CardTitle>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-5 pt-0">
              <div className="space-y-2.5">
                <Label htmlFor="email" className="text-sm font-medium">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  className="h-10"
                  required
                />
              </div>

              <div className="space-y-2.5">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-sm font-medium">
                    Password
                  </Label>
                  <Button variant="link" className="p-0 h-auto text-xs">
                    Forgot password?
                  </Button>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  className="h-10"
                  required
                />
              </div>

              <div className="flex items-center space-x-2 mt-1">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) =>
                    setRememberMe(checked as boolean)
                  }
                  disabled={isLoading}
                />
                <Label
                  htmlFor="remember"
                  className="text-sm text-muted-foreground"
                >
                  Remember me
                </Label>
              </div>
            </CardContent>

            <CardFooter className="pt-2 pb-6 px-6">
              <Button
                type="submit"
                className="w-full py-5 btn-hover-effect"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Signing In...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
