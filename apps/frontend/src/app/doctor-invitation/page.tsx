"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

export default function DoctorInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams?.get("token");

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidToken, setIsValidToken] = useState(false);
  const [isExistingUser, setIsExistingUser] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  // Verify token on page load
  useEffect(() => {
    const verifyToken = async () => {
      if (!token) {
        toast.error("Invalid invitation link");
        router.push("/sign-in");
        return;
      }

      try {
        // Make an API call to verify the token
        const response = await fetch(
          `/api/doctor-invitations/verify?token=${token}`,
        );

        if (!response.ok) {
          const data = await response.json();
          console.error("API error:", data);
          toast.error(data.error || "Invalid or expired invitation link");
          router.push("/sign-in");
          return;
        }

        const data = await response.json();
        setIsValidToken(true);

        // Check if the user already exists
        if (data.invitation.user) {
          setIsExistingUser(true);
          setFormData((prev) => ({
            ...prev,
            name: data.invitation.user.name || "",
            email: data.invitation.user.email || "",
          }));
        } else {
          setIsExistingUser(false);
          setFormData((prev) => ({
            ...prev,
            email: data.invitation.email || "",
          }));
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error verifying token:", error);
        toast.error("Invalid or expired invitation link");
        router.push("/sign-in");
      }
    };

    verifyToken();
  }, [token, router]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Function to sign in the user
  const signInUser = async (email: string, password: string) => {
    try {
      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      if (response.ok) {
        // Redirect to dashboard
        router.push("/");
        router.refresh();
      } else {
        const data = await response.json();
        toast.error(
          data.error ||
            "Failed to sign in automatically. Please sign in manually.",
        );
        router.push("/sign-in");
      }
    } catch (error) {
      console.error("Error signing in:", error);
      toast.error("Failed to sign in automatically. Please sign in manually.");
      router.push("/sign-in");
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (isExistingUser) {
      if (!formData.password) {
        toast.error("Please enter a password");
        return;
      }
    } else {
      if (!formData.name || !formData.email || !formData.password) {
        toast.error("Please fill in all required fields");
        return;
      }
    }

    // Check if passwords match for both cases
    if (formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    setIsSubmitting(true);
    try {
      // Use different endpoints based on whether the user is existing or new
      const endpoint = isExistingUser
        ? "/api/doctor-invitations/accept"
        : "/api/doctor-invitations/verify";

      const payload = isExistingUser
        ? {
            token,
            password: formData.password,
          }
        : {
            token,
            name: formData.name,
            email: formData.email,
            password: formData.password,
          };

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Registration successful");

        // Automatically sign in the user
        await signInUser(formData.email, formData.password);
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to complete registration");
      }
    } catch (error) {
      console.error("Error completing registration:", error);
      toast.error("An error occurred while completing registration");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Verifying invitation...</h1>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Invalid Invitation</h1>
          <p className="mb-4">
            This invitation link is invalid or has expired.
          </p>
          <Button onClick={() => router.push("/sign-in")}>Go to Sign In</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md p-8 space-y-8 bg-card rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold">
            {isExistingUser
              ? "Accept Doctor Invitation"
              : "Complete Your Registration"}
          </h1>
          <p className="text-muted-foreground mt-2">
            {isExistingUser
              ? "You've been invited to join as a doctor."
              : "Create your account to join as a doctor."}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {isExistingUser ? (
            <>
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" name="name" value={formData.name} disabled />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  disabled
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </>
          )}

          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting
              ? "Processing..."
              : isExistingUser
                ? "Accept Invitation"
                : "Complete Registration"}
          </Button>
        </form>

        <div className="text-center mt-4">
          <Button variant="link" onClick={() => router.push("/sign-in")}>
            Back to Sign In
          </Button>
        </div>
      </div>
    </div>
  );
}
